# CI/CD Pipeline for https://github.com/frankysoo/EcommerceHub.git
# Generated with CI/CD Pipeline Implementation Tool

name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Set up environment
        # Customize this section based on your project requirements
        # For example, for a Node.js project:
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
          
      - name: Build
        run: |
          npm install
          npm run build

      - name: Test
        run: |
          npm run test

      - name: Upload Artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: build/
          retention-days: 7

  deploy-to-development:
    name: Deploy to development
    needs: build
    runs-on: ubuntu-latest


    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts
      
      - name: Deploy to development
        run: |
          npm run deploy:dev

  deploy-to-staging:
    name: Deploy to staging
    needs: build
    runs-on: ubuntu-latest


    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts
      
      - name: Deploy to staging
        run: |
          npm run deploy:staging

  deploy-to-production:
    name: Deploy to production
    needs: build
    runs-on: ubuntu-latest
    environment:
      name: production
      url: example.com
    # Requires manual approval for production deployments
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts
      
      - name: Deploy to production
        run: |
          npm run deploy:prod

