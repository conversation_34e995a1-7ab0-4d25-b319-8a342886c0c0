import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import { Redirect, Route } from "wouter";

interface ProtectedRouteProps {
  path: string;
  component: () => React.JSX.Element;
  adminOnly?: boolean;
}

export function ProtectedRoute({
  path,
  component: Component,
  adminOnly = false,
}: ProtectedRouteProps) {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <Route path={path}>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </Route>
    );
  }

  if (!user) {
    return (
      <Route path={path}>
        <Redirect to="/auth" />
      </Route>
    );
  }

  if (adminOnly) {
    console.log("Admin route check:", { path, isAdmin: user.isAdmin, user });

    if (!user.isAdmin) {
      console.log("Access denied: User is not an admin");
      return (
        <Route path={path}>
          <Redirect to="/" />
        </Route>
      );
    } else {
      console.log("Admin access granted");
    }
  }

  return <Route path={path} component={Component} />;
}
